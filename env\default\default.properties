# default.properties
# properties set here will be available to the test execution as environment variables

# sample_key = sample_value

# The path to the gauge reports directory. Should be either relative to the project directory or an absolute path
gauge_reports_dir = reports

# Set as false if gauge reports should not be overwritten on each execution. A new time-stamped directory will be created on each execution.
overwrite_reports = true

# Set to false to disable screenshots on failure in reports.
screenshot_on_failure = true

# The path to the gauge logs directory. Should be either relative to the project directory or an absolute path
logs_directory = logs

# Set to true to use multithreading for parallel execution
enable_multithreading = true

# Number of parallel streams for multi-app testing
parallel_streams = 3

# The path the gauge specifications directory. Takes a comma separated list of specification files/directories.
gauge_specs_dir = e2e/specs

# The default delimiter used read csv files.
csv_delimiter = ,

# Allows steps to be written in multiline
allow_multiline_step = false

# Allows scenario datatable
allow_scenario_datatable = true
