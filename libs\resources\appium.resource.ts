import { SuiteDataManager } from '@libs/gauge/suite.data.manager';
import { remote, RemoteOptions } from 'webdriverio';
import {
  AppConfiguration,
  AppType,
  getAllAppConfigurations,
  getAppConfiguration,
  getCurrentAppConfiguration,
} from './app-config.resource';
import {
  androidCapabilities,
  iosCapabilities,
  SuiteAppiumDriverKey,
} from './config.const';

export type AppiumDriver = WebdriverIO.Browser;

const createAppiumOptions = (appConfig: AppConfiguration): RemoteOptions => {
  const capabilities =
    process.env.PLATFORM_NAME === 'Android'
      ? {
          ...androidCapabilities,
          'appium:appPackage': appConfig.packageName,
          'appium:appActivity': appConfig.activityName,
        }
      : {
          ...iosCapabilities,
          'appium:appPackage': appConfig.packageName,
          'appium:appActivity': appConfig.activityName,
        };

  return {
    hostname: process.env.APPIUM_HOST || 'localhost',
    port: parseInt(process.env.APPIUM_PORT, 10) || appConfig.port,
    logLevel: 'silent',
    capabilities,
  };
};

export const getAppiumDriver = async (
  appType?: AppType,
): Promise<AppiumDriver> => {
  const appConfig = appType
    ? getAppConfiguration(appType)
    : getCurrentAppConfiguration();

  const driverKey = `${SuiteAppiumDriverKey}_${appConfig.appType}`;

  try {
    return SuiteDataManager.get<AppiumDriver>(driverKey);
  } catch (e) {
    const options = createAppiumOptions(appConfig);
    const driver: AppiumDriver = await remote(options);
    SuiteDataManager.put(driverKey, driver);
    return driver;
  }
};

export const terminateDriver = async (appType?: AppType) => {
  try {
    const appConfig = appType
      ? getAppConfiguration(appType)
      : getCurrentAppConfiguration();

    const driverKey = `${SuiteAppiumDriverKey}_${appConfig.appType}`;
    const driver: AppiumDriver = SuiteDataManager.get<AppiumDriver>(driverKey);
    await driver.deleteSession();
  } catch (e) {}
};

export const terminateAllDrivers = async () => {
  const allConfigs = getAllAppConfigurations();
  for (const config of allConfigs) {
    await terminateDriver(config.appType);
  }
};
