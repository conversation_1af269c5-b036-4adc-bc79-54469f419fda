import { getAppiumDriver } from '@libs/resources/appium.resource';
import { DataManager } from '@libs/resources/data-manager.resource';
import { AppType } from '@libs/resources/app-config.resource';
import { Step } from 'gauge-ts';

export class MultiAppLoginSteps {
  
  @Step('Login sanankid app manager with <username> and <password>')
  public async loginAppManager(username: string, password: string) {
    await this.performLogin(username, password, AppType.SANANKIDS_MANAGER);
  }

  @Step('Login sanankid app teacher with <username> and <password>')
  public async loginAppTeacher(username: string, password: string) {
    await this.performLogin(username, password, AppType.SANANKIDS_TEACHER);
  }

  @Step('Login sanankid app parent with <username> and <password>')
  public async loginAppParent(username: string, password: string) {
    await this.performLogin(username, password, AppType.SANANKIDS_PARENT);
  }

  @Step('Login current app with data from CSV')
  public async loginWithCSVData() {
    const credentials = await DataManager.getLoginCredentials();
    if (credentials) {
      await this.performLogin(credentials.username, credentials.password);
    } else {
      throw new Error('No login credentials found in CSV data');
    }
  }

  @Step('Login <appType> app with data from CSV')
  public async loginSpecificAppWithCSVData(appType: string) {
    const appTypeEnum = this.parseAppType(appType);
    const credentials = await DataManager.getLoginCredentials(appTypeEnum);
    if (credentials) {
      await this.performLogin(credentials.username, credentials.password, appTypeEnum);
    } else {
      throw new Error(`No login credentials found in CSV data for ${appType}`);
    }
  }

  private async performLogin(username: string, password: string, appType?: AppType) {
    const driver = await getAppiumDriver(appType);
    
    // Fill username
    const usernameInput = await driver.$('//android.widget.EditText[@text="Số điện thoại của bạn"]');
    await usernameInput.waitForDisplayed({ timeout: 60000 });
    await usernameInput.setValue(username);

    // Click continue button
    const continueBtn = await driver.$('//android.widget.TextView[@text="Tiếp Tục"]');
    await continueBtn.waitForDisplayed();
    await continueBtn.click();

    // Fill password
    const passwordInput = await driver.$('//android.widget.EditText[@text="Nhập mật khẩu"]');
    await passwordInput.waitForDisplayed();
    await passwordInput.setValue(password);

    // Click login button
    const loginBtn = await driver.$('//android.widget.TextView[@text="Đăng Nhập"]');
    await loginBtn.waitForDisplayed();
    await loginBtn.click();
  }

  private parseAppType(appTypeString: string): AppType {
    const normalizedType = appTypeString.toLowerCase().replace(/\s+/g, '-');
    
    switch (normalizedType) {
      case 'manager':
      case 'sanankids-app-manager':
        return AppType.SANANKIDS_MANAGER;
      case 'teacher':
      case 'sanankids-app-teacher':
        return AppType.SANANKIDS_TEACHER;
      case 'parent':
      case 'sanankids-app-parent':
        return AppType.SANANKIDS_PARENT;
      default:
        throw new Error(`Unknown app type: ${appTypeString}`);
    }
  }
}
