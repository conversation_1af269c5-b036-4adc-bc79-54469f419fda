# Multi-App Parallel Testing Example

tags: mobile,android,multi-app

## Parallel App Testing Example

### Test Manager App
* Login sanankid app manager with "0854124589" and "27041998"
* Mobile: Click with "<PERSON>u<PERSON>n lý toàn trường"
* Mobile: Click button allow permission controler
* Mobile: Verify exact "Dashboard" is visible
* Mobile: Capture screenshot

### Test Teacher App  
* Login sanankid app teacher with "0936275212" and "27041998"
* Mobile: Click with "autotest_001"
* Mobile: Click button allow permission controler
* Mobile: Verify exact "Điểm danh" is visible
* Mobile: Capture screenshot

### Test Parent App
* Login sanankid app parent with "0866113736" and "anhduc200510"
* Mobile: Click with "Bé Tèo"
* Mobile: Verify exact "Thông tin bé" is visible
* Mobile: Capture screenshot
