import { readFileSync } from 'fs';
import { parse } from 'csv-parse/sync';
import { getCurrentAppConfiguration, AppType, getAppConfiguration } from './app-config.resource';

export interface TestData {
  [key: string]: string;
}

export class DataManager {
  private static dataCache: Map<string, TestData[]> = new Map();

  /**
   * Read CSV data for specific app type
   */
  public static async getTestData(appType?: AppType): Promise<TestData[]> {
    const appConfig = appType ? 
      getAppConfiguration(appType) : 
      getCurrentAppConfiguration();
    
    const cacheKey = appConfig.appType;
    
    // Return cached data if available
    if (this.dataCache.has(cacheKey)) {
      return this.dataCache.get(cacheKey)!;
    }

    try {
      const csvContent = readFileSync(appConfig.dataFilePath, 'utf-8');
      const records = parse(csvContent, {
        columns: true,
        skip_empty_lines: true,
        delimiter: ',',
      }) as TestData[];

      // Cache the data
      this.dataCache.set(cacheKey, records);
      return records;
    } catch (error) {
      console.error(`Error reading CSV file for ${appConfig.displayName}:`, error);
      return [];
    }
  }

  /**
   * Get specific test data row by index
   */
  public static async getTestDataRow(index: number, appType?: AppType): Promise<TestData | null> {
    const data = await this.getTestData(appType);
    return data[index] || null;
  }

  /**
   * Get test data row by field value
   */
  public static async getTestDataByField(
    fieldName: string, 
    fieldValue: string, 
    appType?: AppType
  ): Promise<TestData | null> {
    const data = await this.getTestData(appType);
    return data.find(row => row[fieldName] === fieldValue) || null;
  }

  /**
   * Get all test data for all apps
   */
  public static async getAllAppsTestData(): Promise<Map<AppType, TestData[]>> {
    const result = new Map<AppType, TestData[]>();
    
    for (const appType of Object.values(AppType)) {
      const data = await this.getTestData(appType);
      result.set(appType, data);
    }
    
    return result;
  }

  /**
   * Clear data cache
   */
  public static clearCache(appType?: AppType): void {
    if (appType) {
      this.dataCache.delete(appType);
    } else {
      this.dataCache.clear();
    }
  }

  /**
   * Get login credentials for specific app
   */
  public static async getLoginCredentials(appType?: AppType): Promise<{username: string, password: string} | null> {
    const data = await this.getTestDataRow(0, appType);
    if (data && data.username && data.password) {
      return {
        username: data.username,
        password: data.password
      };
    }
    return null;
  }

  /**
   * Get profile name for specific app
   */
  public static async getProfileName(appType?: AppType): Promise<string | null> {
    const data = await this.getTestDataRow(0, appType);
    return data?.profileName || null;
  }

  /**
   * Get school name for specific app
   */
  public static async getSchoolName(appType?: AppType): Promise<string | null> {
    const data = await this.getTestDataRow(0, appType);
    return data?.schoolName || null;
  }
}
