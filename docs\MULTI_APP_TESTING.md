# Multi-App Testing Guide

## 🎯 Tổng quan

Hệ thống autotest mobile hiện đã được nâng cấp để hỗ trợ chạy nhiều app song song (multi-app parallel testing). Bạn có thể:

- Chạy test cho từng app riêng biệt
- Chạy test song song cho nhiều app cùng lúc
- Quản lý data riêng biệt cho từng app
- Cấu hình port và device khác nhau cho từng app

## 🏗️ Cấu trúc mới

### **1. App Configuration**
```typescript
// libs/resources/app-config.resource.ts
export enum AppType {
  SANANKIDS_PARENT = 'sanankids-app-parent',
  SANANKIDS_MANAGER = 'sanankids-app-manager', 
  SANANKIDS_TEACHER = 'sanankids-app-teacher',
}
```

### **2. Environment Configuration**
```
env/
├── sanankids-app-manager/
│   └── sanankids-app-manager.properties
├── sanankids-app-teacher/
│   └── sanankids-app-teacher.properties
└── sanankids-app-parent/
    └── sanankids-app-parent.properties
```

### **3. Data Management**
Mỗi app có file CSV data riêng:
```
e2e/specs/
├── sanankids-app-manager/timekeeping/test-data/data.csv
├── sanankids-app-teacher/timekeeping/test-data/data.csv
└── sanankids-app-parent/test-data/data.csv
```

## 🚀 Cách sử dụng

### **1. Chạy test cho từng app riêng biệt**

```bash
# Chạy test cho App Manager
npm run test:app-manager

# Chạy test cho App Teacher  
npm run test:app-teacher

# Chạy test cho App Parent
npm run test:app-parent
```

### **2. Chạy test song song cho nhiều app**

```bash
# Chạy test song song với 3 processes
npm run test:parallel

# Chạy test song song (auto detect số processes)
npm run test:multi-app
```

### **3. Sử dụng trong Spec Files**

#### **Cách 1: Sử dụng tags để auto-detect app**
```gauge
# SANAN Kid - App manager
tags: mobile,sanankid-app-manager,android

## Test scenario
* Login current app with data from CSV
* Mobile: Click with "Quản lý toàn trường"
```

#### **Cách 2: Chỉ định app cụ thể**
```gauge
## Test scenario  
* Login sanankid app manager with "0854124589" and "27041998"
* Login sanankid app teacher with "0936275212" and "27041998"
```

#### **Cách 3: Sử dụng data từ CSV**
```gauge
## Test scenario
* Login manager app with data from CSV
* Login teacher app with data from CSV
```

## ⚙️ Cấu hình

### **1. Appium Server Configuration**

Mỗi app sử dụng port khác nhau:
- **App Parent**: Port 4723
- **App Manager**: Port 4724  
- **App Teacher**: Port 4725

Khởi động multiple Appium servers:
```bash
# Terminal 1
appium --port 4723

# Terminal 2  
appium --port 4724

# Terminal 3
appium --port 4725
```

### **2. Device Configuration**

Cập nhật `src/test/resources/setup_devices.json` để có nhiều devices:
```json
[
  {
    "device_name": "device_manager",
    "device_status": true,
    "device_UDID": "BQ5H9545XWIJP7UK", 
    "OS": "Android"
  },
  {
    "device_name": "device_teacher",
    "device_status": true,
    "device_UDID": "06829251CS100381",
    "OS": "Android" 
  },
  {
    "device_name": "device_parent",
    "device_status": true,
    "device_UDID": "emulator-5554",
    "OS": "Android"
  }
]
```

### **3. Parallel Execution Settings**

Trong `env/default/default.properties`:
```properties
# Enable multithreading for parallel execution
enable_multithreading = true

# Number of parallel streams
parallel_streams = 3
```

## 📝 Ví dụ thực tế

### **Example 1: Test riêng biệt cho từng app**

```gauge
# App Manager Test
tags: mobile,sanankid-app-manager,android
table: e2e/specs/sanankids-app-manager/timekeeping/test-data/data.csv

## Manager Login Test
* Login current app with data from CSV
* Mobile: Click with "Quản lý toàn trường"
* Mobile: Verify exact "Dashboard" is visible
```

### **Example 2: Test song song nhiều app**

```gauge  
# Multi-App Test
tags: mobile,android

## Parallel App Testing
* Login sanankid app manager with "0854124589" and "27041998"
* Login sanankid app teacher with "0936275212" and "27041998"  
* Login sanankid app parent with "0866113736" and "anhduc200510"
```

## 🔧 Troubleshooting

### **1. Port conflicts**
Nếu gặp lỗi port conflict, kiểm tra:
- Appium servers đang chạy trên đúng ports
- Không có process nào khác sử dụng ports 4723-4725

### **2. Device conflicts**  
Nếu gặp lỗi device busy:
- Đảm bảo mỗi app sử dụng device khác nhau
- Kiểm tra device_status trong setup_devices.json

### **3. Data file not found**
Nếu không tìm thấy CSV file:
- Kiểm tra đường dẫn trong app-config.resource.ts
- Đảm bảo file CSV tồn tại và có đúng format

## 📊 Performance Tips

1. **Sử dụng emulators** thay vì real devices cho parallel testing
2. **Giới hạn số parallel processes** dựa trên hardware (recommend: 2-3)
3. **Monitor memory usage** khi chạy nhiều app cùng lúc
4. **Sử dụng data cache** để tránh đọc CSV nhiều lần

## 🎉 Kết luận

Với cấu trúc mới này, bạn có thể:
- ✅ Chạy test cho nhiều app song song
- ✅ Quản lý data riêng biệt cho từng app  
- ✅ Cấu hình flexible cho từng app
- ✅ Scale testing dễ dàng

Happy Testing! 🚀
