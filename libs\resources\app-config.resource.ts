import { getCurrentSpecInfo } from '@libs/utils/gauge.util';

export enum AppType {
  SANANKIDS_PARENT = 'sanankids-app-parent',
  SANANKIDS_MANAGER = 'sanankids-app-manager',
  SANANKIDS_TEACHER = 'sanankids-app-teacher',
}

export interface AppConfiguration {
  appType: AppType;
  packageName: string;
  activityName: string;
  displayName: string;
  dataFilePath: string;
  port: number;
}

const APP_CONFIGURATIONS: Record<AppType, AppConfiguration> = {
  [AppType.SANANKIDS_PARENT]: {
    appType: AppType.SANANKIDS_PARENT,
    packageName: 'com.sanankids.parent.dev',
    activityName: 'com.sanankids.parent.MainActivity',
    displayName: 'SananKids Parent',
    dataFilePath: 'e2e/specs/sanankids-app-parent/test-data/data.csv',
    port: 4723,
  },
  [AppType.SANANKIDS_MANAGER]: {
    appType: AppType.SANANKIDS_MANAGER,
    packageName: 'com.sanan.sanankids.manager.dev',
    activityName: 'com.sanan.sanankids.manager.MainActivity',
    displayName: 'SananKids Manager',
    dataFilePath: 'e2e/specs/sanankids-app-manager/timekeeping/test-data/data.csv',
    port: 4724,
  },
  [AppType.SANANKIDS_TEACHER]: {
    appType: AppType.SANANKIDS_TEACHER,
    packageName: 'com.sanan.sanankids.teacher.dev',
    activityName: 'com.sanan.sanankids.teacher.MainActivity',
    displayName: 'SananKids Teacher',
    dataFilePath: 'e2e/specs/sanankids-app-teacher/timekeeping/test-data/data.csv',
    port: 4725,
  },
};

/**
 * Determine app type from spec tags or environment
 */
export const getAppTypeFromContext = (): AppType => {
  // Try to get from environment variable first
  const appTypeEnv = process.env.APP_TYPE as AppType;
  if (appTypeEnv && Object.values(AppType).includes(appTypeEnv)) {
    return appTypeEnv;
  }

  // Try to get from current spec tags
  try {
    const specInfo = getCurrentSpecInfo();
    if (specInfo && specInfo.tags) {
      for (const tag of specInfo.tags) {
        if (Object.values(AppType).includes(tag as AppType)) {
          return tag as AppType;
        }
      }
    }
  } catch (e) {
    // Fallback if spec info is not available
  }

  // Try to get from spec file path
  try {
    const specInfo = getCurrentSpecInfo();
    if (specInfo && specInfo.specFilePath) {
      const filePath = specInfo.specFilePath.toLowerCase();
      
      if (filePath.includes('sanankids-app-manager')) {
        return AppType.SANANKIDS_MANAGER;
      }
      if (filePath.includes('sanankids-app-teacher')) {
        return AppType.SANANKIDS_TEACHER;
      }
      if (filePath.includes('sanankids-app-parent')) {
        return AppType.SANANKIDS_PARENT;
      }
    }
  } catch (e) {
    // Fallback if spec info is not available
  }

  // Default fallback
  return AppType.SANANKIDS_PARENT;
};

/**
 * Get app configuration for specific app type
 */
export const getAppConfiguration = (appType: AppType): AppConfiguration => {
  return APP_CONFIGURATIONS[appType];
};

/**
 * Get current app configuration based on context
 */
export const getCurrentAppConfiguration = (): AppConfiguration => {
  const currentAppType = getAppTypeFromContext();
  return getAppConfiguration(currentAppType);
};

/**
 * Get all available app configurations
 */
export const getAllAppConfigurations = (): AppConfiguration[] => {
  return Object.values(APP_CONFIGURATIONS);
};

/**
 * Check if app type is valid
 */
export const isValidAppType = (appType: string): appType is AppType => {
  return Object.values(AppType).includes(appType as AppType);
};
